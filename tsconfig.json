{"compilerOptions": {"module": "esNext", "target": "ESNext", "jsx": "react-jsx", "moduleResolution": "node", "alwaysStrict": true, "sourceMap": false, "allowSyntheticDefaultImports": true, "removeComments": false, "preserveConstEnums": true, "experimentalDecorators": true, "noImplicitReturns": true, "noUnusedLocals": true, "noUnusedParameters": false, "noImplicitAny": false, "noImplicitThis": false, "resolveJsonModule": true, "outDir": "lib", "rootDir": "./", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "ice": [".ice"]}}, "include": ["src", ".pegasus"], "exclude": ["node_modules", "build", "public"]}