import type { Request, Response } from '@ice/app';
import bodyParser from 'body-parser';
import getSkeletonMock from './getSkeleton.mock';
import getDataMock from './getData.mock';

export default {
  'POST /hsf/com.taobao.arkact.handler.index': (request: Request, response: Response) => {
    const map = {
      getSkeleton: getSkeletonMock,
      getData: getDataMock,
    };

    bodyParser.json()(request, response, () => {
      /**
       * request.body 是请求的参数
       * response.send 返回的就是需要mock的数据
       */
      response.send(map[request.body.args[0]]);
    });
  },
};