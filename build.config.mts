import { defineConfig } from "@ice/pkg";

// https://pkg.ice.work/reference/config-list
export default defineConfig({
  sourceMaps: true,
  transform: {
    formats: ["esm", "es2017"],
  },
  bundle: {
    formats: ["cjs", "es2017"],
    compileDependencies: true,
  },
  alias: {
    "@": "./src",
  },
  plugins: [
    [
      "@ali/build-plugin-ice-pegasus-pkg",
      {
        defaultPreviewMode: "ssr",
        useVisualDev: false,
      },
    ],
    "@ice/pkg-plugin-jsx-plus",
    [
      "@ali/build-plugin-pegasus-base-ssr",
      {
        mode: "api",
        useArkIce: true,
      },
    ],
    "@ali/ark-ice-builder/build.cjs",
  ],
});
