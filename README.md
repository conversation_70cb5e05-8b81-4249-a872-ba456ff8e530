# 本地开发说明
* 如不需要调试SSR和依赖方舟的能力，可以使用 `npm run preview` 进行本地开发，改动代码毫秒级响应

# 魔镜（管理你的端到端测试用例）
* 介绍和使用指南：https://aliyuque.antfin.com/magic/book/about

# 开发者说明
* 默认使用ark-ice-solution（方舟渲染Solution）(https://code.alibaba-inc.com/psolution/ark-ice-solution/blob/master/package.json)，提供完备的占位骨架+SSR预览的预览服务

# 模块开发实践
* 推荐阅读 https://aliyuque.antfin.com/tbpromotion/hxnlbf/wsybunn034fimssk
* ice：https://ice3.alibaba-inc.com/


// "defaultBannerImg": {
//   "type": "string",
//   "title": "默认状态Banner图",
//   "description": "有可用红包时显示的Banner图片URL",
//   "default": "https://img.alicdn.com/imgextra/i2/O1CN01L2cES11EUBGKjtWH1_!!6000000000354-2-tps-2208-288.png",
//   "format": "image"
// },
// "emptyBannerImg": {
//   "type": "string",
//   "title": "无红包状态Banner图",
//   "description": "红包用完/发放失败/未命中人群时显示的Banner图片URL",
//   "default": "https://img.alicdn.com/imgextra/i3/O1CN01xAZI6f1tCZfO8ju87_!!6000000005864-2-tps-2208-288.png",
//   "format": "image"
// },