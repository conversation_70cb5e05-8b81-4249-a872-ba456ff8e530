import { useEffect, useState } from 'react';
import View from '@ali/pcom-ice-view';
import Picture from '@ali/picture';
import Item from './components/Item';
import styles from './index.module.css';
import { requestData } from '@ali/pcom-uniapi-solution-mtop';

export default (props) => {
  console.log('props.data:::', props.data);
  const { $attr = {}, $theme = {}, items = [] } = props.data || {};
  const [serverTime, setServerTime] = useState('');

  useEffect(() => {
    requestData({
        api: 'mtop.common.gettimestamp',
        v: '1.0',
        data: {},
    }).then((res: any) => {
      const t = res?.data?.t;
      if (t) {
        setServerTime(
          new Date(
            Number(t),
          ).toString(),
        );
      }
    });
  }, []);

  return (
    <View className={styles.mod}>
      <View className={styles.logos} style={{ background: $theme.themeColor }}>
        <Picture
          className={styles.logoArk}
          width={79}
          height={70}
          source={{ uri: 'https://gw.alicdn.com/imgextra/i4/O1CN01DJI00V1p4CVmjEtFM_!!6000000005306-2-tps-908-800.png' }}
          optimize={{
            quality: 'original',
          }}
        />
        <View className={styles.text}>x</View>
        <Picture
          className={styles.logoIce}
          source={{ uri: 'https://gw.alicdn.com/imgextra/i2/O1CN01CPNvCj1KhMHoIeBGW_!!6000000001195-2-tps-584-332.png' }}
          width={123}
          height={70}
        />
      </View>
      <ul>
        {!$attr.hidden && items.map(v => (
          <li key={v.key} style={{ padding: 15 }}>
            <b style={{ fontSize: 18 }}>{v.key}</b>
            <p style={{ paddingTop: 10 }}>{v.value}</p>
          </li>
        ))}
      </ul>
      <Item mode="模块模式" welcome={$attr.welcome} />
      <div style={{ fontSize: 12, textAlign: 'center' }}>{serverTime}</div>
    </View>
  );
};
