import { isTrue, mtopRequest } from "@ali/pcom-tbpc-venue-utils";

interface ICouponParams {
    bizCode: string;
    bizParams: string;
    asac: string;
}
interface ICouponRes {
    success: boolean;
    message: string;
    [key: string]: any;
}

interface IRedPackageParams {
    bizCode: string;
    bizParams: string;
    asac: string;
}
interface IRedPackageRes {
    success: boolean;
    message: string;
    [key: string]: any;
}

export const requestRead = async ({
    bizCode,
    bizParams,
    asac,
}: IRedPackageParams, props: any = {}): Promise<IRedPackageRes> => {
    const requestParams = {
        bizCode,
        bizParams,
        asac,
    };
    const api = "mtop.mktcell.gateway.read";
    try {
        const res = await mtopRequest({
            api,
            data: requestParams,
        });
        if (isTrue(res.success)) {
            return {
                data: res?.data || {},
                message: "",
                success: true,
            };
        }
        return {
            data: {},
            message: "",
            success: false,
        };
    } catch (err) {
        console.error("mtop.mktcell.gateway.read failed:", err);
        return {
            data: err,
            message: err?.message || '',
            success: false,
        };
    }
};


export const requestWrite = async ({
    bizCode,
    bizParams,
    asac,
}: ICouponParams, props: any = {}): Promise<ICouponRes> => {
    const requestParams = {
        bizCode,
        bizParams,
        asac,
    };
    const api = "mtop.mktcell.gateway.write";
    try {
        const res = await mtopRequest({
            api,
            data: requestParams,
        });
        if (isTrue(res?.success)) {
            return {
                data: res?.data || {},
                message: "",
                success: true,
            };
        }
        return {
            data: {},
            message: res?.message || res?.msg || "",
            success: false,
        };
    } catch (err) {
        console.error("mtop.mktcell.gateway.write failed:", err);
        return {
            data: err,
            message: err?.message || '',
            success: false,
        };
    }
};
