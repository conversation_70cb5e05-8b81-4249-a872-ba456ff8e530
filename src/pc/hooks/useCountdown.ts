import { useState, useEffect, useRef } from "react";
import { queryCurrentTimestamp } from "@ali/pcom-tbpc-venue-utils";

interface CountdownTime {
  days: number;
  hours: string;
  minutes: string;
  seconds: string;
  totalMs: number;
}

/**
 * 倒计时 Hook
 * @param endTime 结束时间戳（毫秒）
 * @returns 倒计时数据和状态
 */
export const useCountdown = (endTime: number) => {
  // 使用一个简单的计数器来强制组件重新渲染
  const [tick, setTick] = useState(0);

  // 状态定义
  const [countdownTime, setCountdownTime] = useState<CountdownTime>({
    days: 0,
    hours: "00",
    minutes: "00",
    seconds: "00",
    totalMs: 0
  });
  const [isExpired, setIsExpired] = useState(false);
  const [isActive, setIsActive] = useState(true);

  // 引用值定义
  const startTimestamp = useRef<number>(Date.now());
  const timerRef = useRef<ReturnType<typeof setInterval>>();

  // 格式化时间函数
  const formatTime = (intervalMs: number): CountdownTime => {
    if (intervalMs <= 0) {
      return {
        days: 0,
        hours: "00",
        minutes: "00",
        seconds: "00",
        totalMs: 0
      };
    }

    const days = Math.floor(intervalMs / (1000 * 60 * 60 * 24));
    const hours = Math.floor((intervalMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      .toString()
      .padStart(2, "0");
    const minutes = Math.floor((intervalMs % (1000 * 60 * 60)) / (1000 * 60))
      .toString()
      .padStart(2, "0");
    const seconds = Math.floor((intervalMs % (1000 * 60)) / 1000)
      .toString()
      .padStart(2, "0");

    return {
      days,
      hours,
      minutes,
      seconds,
      totalMs: intervalMs
    };
  };

  // 更新倒计时状态
  const updateCountdown = () => {
    const now = startTimestamp.current;
    const remainingTime = endTime - now;

    if (remainingTime <= 0) {
      // 倒计时结束
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = undefined;
      }
      setIsExpired(true);
      setIsActive(false);
      setCountdownTime(formatTime(0));
    } else {
      // 更新倒计时
      const formattedTime = formatTime(remainingTime);
      setCountdownTime(formattedTime);

      // 增加tick计数，强制组件重新渲染
      setTick(prevTick => prevTick + 1);
    }
  };

  // 获取服务器时间
  const fetchServerTime = async (): Promise<boolean> => {
    try {
      const { t = "" } = await queryCurrentTimestamp();
      if (t) {
        startTimestamp.current = parseInt(t);
      } else {
        startTimestamp.current = Date.now();
      }

      // 更新倒计时状态
      updateCountdown();

      // 如果已过期，返回false表示不需要继续倒计时
      return endTime > startTimestamp.current;
    } catch (err) {
      console.error("获取服务器时间失败:", err);
      startTimestamp.current = Date.now();
      updateCountdown();
      return endTime > startTimestamp.current;
    }
  };

  // 初始化和清理
  useEffect(() => {
    // 初始化：获取服务器时间并启动倒计时
    fetchServerTime().then(shouldContinue => {
      if (shouldContinue) {
        // 清除可能存在的旧定时器
        if (timerRef.current) {
          clearInterval(timerRef.current);
        }

        // 设置新的定时器，每秒更新一次
        timerRef.current = setInterval(() => {
          startTimestamp.current += 1000;
          updateCountdown();
        }, 1000);
      }
    });

    // 添加页面可见性变化监听
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，重新获取服务器时间
        console.log('页面可见，重新获取服务器时间');
        fetchServerTime().then(shouldContinue => {
          // 如果倒计时已结束，不需要继续
          if (!shouldContinue) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
              timerRef.current = undefined;
            }
          }
        });
      }
    };

    // 添加监听器
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = undefined;
      }
      // 移除监听器
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [endTime]); // 只在endTime变化时重新执行

  // 计算格式化的显示文本
  let formattedText = '';

  // 根据剩余时间显示不同格式
  if (countdownTime.days >= 1) {
    // 剩余时间超过24小时：显示天数
    formattedText = `距失效还有${countdownTime.days}天`;
  } else {
    // 剩余时间不足24小时：显示时分秒，格式为 HH:MM:SS
    formattedText = `距失效${countdownTime.hours}:${countdownTime.minutes}:${countdownTime.seconds}`;
  }

  // 返回结果，包括tick以确保组件能够响应更新
  return {
    countdownTime,
    isExpired,
    isActive,
    formattedText,
    tick,
  };
};
