.container {
  border-radius: 8px;
  background: #FFFFFF;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  box-sizing: border-box;
  margin-top: 12px;
  align-items: center;
  width: 100%;
  height: 72px;
}

.priceContainer {
  border-radius: 8px;
  background: #FFFFFF;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 96px;
  height: 72px;
  z-index: 0;
  flex-shrink: 0;
}

.topDecoration {
  width: 16px;
  height: 16px;
  position: absolute;
  left: 80px;
  top: -8px;
}

.bottomDecoration {
  width: 16px;
  height: 16px;
  position: absolute;
  left: 80px;
  top: 64px;
}

.priceWrapper {
  height: 28px;
  display: flex;
  flex-direction: row;
  align-items: baseline;
}

.currencySymbol {
  font-size: 16px;
  font-weight: 600;
  line-height: 20px;
  color: #FF0036;
  margin-right: 2px;
}

.priceValue {
  font-size: 28px;
  font-weight: 600;
  line-height: 1;
  color: #FF0036;
  margin-left: 0px;
}

.infoContainer {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  box-sizing: border-box;
  padding: 0px 0px 0px 8px;
  justify-content: center;
  flex: 1;
  height: 72px;
  margin-left: 0px;
  position: relative;
}

.titleWrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  height: 24px;
}

.title {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  color: #000000EB;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  flex: 1;
  display: inline-block;
}

.description {
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  color: #00000085;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  width: 100%;
  display: inline-block;
  margin-top: 0px;
}

.useButton, .claimButton {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  padding: 4px 12px;
  border: 1px solid;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  text-decoration: none;
  text-align: center;
  white-space: nowrap;
  margin-right: 20px;
  cursor: pointer;
  background: transparent;
  transition: all 0.2s ease;
  color: #fff;
  background-color: #FF0036;
}
