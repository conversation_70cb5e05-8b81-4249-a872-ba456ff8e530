import React from 'react';
import styles from './index.module.less';
import { useCountdown } from '../../hooks/useCountdown';

interface ConsumptionVoucherProps {
  id?: string;
  price: number;
  benefitTitle: string;
  displayStartFee: string;
  description: string;
  jumpUrl?: string;
  textColor?: string;
  status?: string;
  effectiveEndTimestamp?: number;
  effectiveStartTimestamp?: number;
  onClaim?: () => void;
  claiming?: boolean;
  hasReceived?: boolean;
  showClaimButton?: boolean;
  isWhiteTheme?: boolean;
}

const ConsumptionVoucher: React.FC<ConsumptionVoucherProps> = ({
  id,
  price,
  displayStartFee,
  description,
  benefitTitle,
  jumpUrl,
  status,
  textColor = '#FF3333',
  effectiveEndTimestamp,
  effectiveStartTimestamp,
  onClaim,
  claiming = false,
  hasReceived = false,
  showClaimButton = false,
  isWhiteTheme = false
}) => {
  const { formattedText, isExpired } = useCountdown(effectiveEndTimestamp || 0);

  // 计算生效时间描述
  const getEffectiveDescription = () => {
    if (status === 'inEffective' && effectiveStartTimestamp && effectiveEndTimestamp) {
      const startTime = effectiveStartTimestamp;
      
      // 格式化日期为"几月几号"
      const formatDate = (timestamp: number) => {
        const date = new Date(timestamp);
        const month = date.getMonth() + 1;
        const day = date.getDate();
        return `${month}月${day}号`;
      };

      return `${formatDate(startTime)}生效`;
    }
    return status === 'effective' ? formattedText : `满¥${displayStartFee}可用`;
  };

  const containerStyle = isWhiteTheme ? {
    background: '#FF0036',
    color: '#FFFFFF'
  } : {};

  const textStyle = isWhiteTheme ? { color: '#FFFFFF' } : {};
  const priceStyle = isWhiteTheme ? { color: '#FFFFFF' } : {};

  return (
    <div id={id} className={styles.container} style={containerStyle}>
      <div className={styles.priceContainer} style={containerStyle}>
        {
          isWhiteTheme ?
            <svg width="16" height="16"  className={styles.topDecoration}>
              <circle cx="8" cy="8" r="8" fill="#fff" />
            </svg>
            :
            <img
              src="https://img.alicdn.com/imgextra/i2/6000000006258/O1CN01a8Wrxh1w6DefI2Kym_!!6000000006258-2-gg_dtc.png"
              className={styles.topDecoration}
            />
        }
        <div className={styles.priceWrapper}>
          <span className={styles.currencySymbol} style={priceStyle}>¥</span>
          <span className={styles.priceValue} style={priceStyle}>{price}</span>
        </div>
        {
          isWhiteTheme ?
            <svg width="16" height="16" className={styles.bottomDecoration}>
              <circle cx="8" cy="8" r="8" fill="#fff" />
            </svg>
            :
            <img
              src="https://img.alicdn.com/imgextra/i2/6000000006258/O1CN01a8Wrxh1w6DefI2Kym_!!6000000006258-2-gg_dtc.png"
              className={styles.bottomDecoration}
            />
        }
      </div>
      <div className={styles.infoContainer}>
        <div className={styles.titleWrapper}>
          <span className={styles.title} style={textStyle}>
            {status === 'effective' || status === 'inEffective' ? `满¥${displayStartFee}可用` :  benefitTitle}
          </span>
        </div>
        <span className={styles.description} style={textStyle}>
          {getEffectiveDescription()}
        </span>

        {jumpUrl && status === 'effective' && !isExpired && (
          <a
            href={jumpUrl}
            target='_blank'
            className={styles.useButton}
          >
            去使用
          </a>
        )}

        {showClaimButton && status !== 'effective' && !hasReceived && onClaim && (
          <button
            className={styles.claimButton}
            onClick={onClaim}
            disabled={claiming}
            style={{
              opacity: claiming ? 0.6 : 1,
              cursor: claiming ? 'not-allowed' : 'pointer'
            }}
          >
            {claiming ? '领取中...' : '立即领取'}
          </button>
        )}
      </div>
    </div>
  );
};

export default ConsumptionVoucher;
