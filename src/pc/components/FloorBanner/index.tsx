import { CSSProperties, useMemo, useState, useEffect, useRef } from "react";
import { AdaptLayout } from '@ali/pcom-tbpc-mod-container';
import styles from './index.module.less';

export default (props) => {
  const { $attr } = props?.data;
  const {
    imgUrl,
    marginTop,
    marginBottom,
    height,
    bgColor = 'transparent',
    imgRadio = 2,
    borderRadius = 12,
    paddingTop = 12,
    paddingBottom = 12,
    jumpUrl,
    minHeight = 144, // 添加最小高度配置，默认为144px
  } = $attr || {};
  
  // 设置默认高度为配置的minHeight或144px
  const [cHeight, setCHeight] = useState(height || minHeight);
  const [imageLoaded, setImageLoaded] = useState(false);
  const imgRef = useRef(null);
  const prevImgUrl = useRef(imgUrl);

  // 当图片URL变化时，重置加载状态
  useEffect(() => {
    if (imgUrl !== prevImgUrl.current) {
      setImageLoaded(false);
      prevImgUrl.current = imgUrl;
    }
  }, [imgUrl]);

  // 预加载图片
  useEffect(() => {
    if (!imgUrl) return;
    
    const img = new Image();
    img.src = imgUrl;
    
    img.onload = (e) => {
      // 计算高度，如果没有指定高度，则根据图片比例计算
      if (!height) {
        const hh = e?.target?.height ? (e?.target?.height / imgRadio) : minHeight;
        setCHeight(Math.max(hh, minHeight)); // 确保高度不小于最小高度
      }
      setImageLoaded(true);
    };
    
    img.onerror = () => {
      // 图片加载失败时也设置为已加载，避免一直显示加载状态
      setImageLoaded(true);
      if (!height) {
        setCHeight(minHeight); // 设置为配置的最小高度
      }
    };
  }, [imgUrl, imgRadio, height, minHeight]);

  const style = useMemo(() => {
    const temp: CSSProperties = {};
    if (bgColor) {
      temp.backgroundColor = bgColor;
    }
    if (borderRadius) {
      temp.borderRadius = `${borderRadius}px`;
    }
    // 始终设置高度，避免闪动
    temp.height = `${cHeight}px`;
    // 设置最小高度
    temp.minHeight = `${minHeight}px`;
    if (marginTop) {
      temp.marginTop = `${marginTop}px`;
    }
    if (marginBottom) {
      temp.marginBottom = `${marginBottom}px`;
    }
    if (paddingTop) {
      temp.paddingTop = `${paddingTop}px`;
    }
    if (paddingBottom) {
      temp.paddingBottom = `${paddingBottom}px`;
    }
    return temp;
  }, [cHeight, minHeight, bgColor, borderRadius, marginTop, marginBottom, paddingTop, paddingBottom]);

  const imgStyle = useMemo(() => {
    if (height) return {};
    
    const imgStyle: CSSProperties = {};
    imgStyle.height = 'auto';
    imgStyle.maxWidth = `${1104 * imgRadio}px`;
    imgStyle.transform = `scale(${1/imgRadio})`;
    
    // 添加过渡效果
    imgStyle.transition = 'opacity 0.3s ease-in-out';
    imgStyle.opacity = imageLoaded ? 1 : 0;
    
    return imgStyle;
  }, [height, imgRadio, imageLoaded]);

  if (!imgUrl) return null;

  return (
    <AdaptLayout>
      <div className={styles.container} style={style}>
        {/* 添加骨架屏，在图片加载前显示 */}
        {!imageLoaded && (
          <div className={styles.skeleton}></div>
        )}
        
        <img 
          ref={imgRef}
          style={imgStyle} 
          src={imgUrl} 
          alt=""
          onLoad={() => setImageLoaded(true)}
          onError={() => setImageLoaded(true)}
        />
        
        {jumpUrl && <a href={jumpUrl} target="_blank" rel="noreferrer" className={styles.jump} data-spm="d_floorBanner" />}
      </div>
    </AdaptLayout>
  );
};
