.container {
  display: flex;
  width: 100%;
  position: relative;
  justify-content: center;
  align-items: center;
  overflow: hidden; // 确保骨架屏不会溢出容器
  .jump {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    cursor: pointer;
  }
  
  .skeleton {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%; // 填满整个容器
    background: linear-gradient(90deg, #f5f5f5 25%, #eaeaea 50%, #f5f5f5 75%);
    background-size: 200% 100%;
    animation: shimmer 2s ease-in-out infinite;
    border-radius: inherit; // 继承容器的圆角
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

