import { useEffect, useState } from "react";
import ModLayout, {
  LayoutRadioEnum,
  AdaptLayout,
  AdaptRow,
  AdaptCol,
} from "@ali/pcom-tbpc-mod-container";
import { toast } from '@ali/pcom-ark-pc-toast';
import MyHeader from "@ali/pcom-tbpc-component-venue-modheader";
import Coupon from "./components/Coupon";
import { isTrue } from "@ali/pcom-tbpc-venue-utils";
import { requestWrite, requestRead } from "./utils/request";
import styles from "./index.module.less";


const { Header } = ModLayout;

interface Props {
  data?: {
    $attr?: {
      hidden?: boolean;
      marginBottom?: number;
      defaultBannerImg?: string;
      emptyBannerImg?: string;
      couponBackgroundColor?: string; // 添加券背景色配置
      channel?: string; // 添加渠道配置
      asac?: string; // 添加asac配置
      [key: string]: any;
    };
    $theme?: Record<string, any>;
  };
}

interface CouponData {
  effectiveStartTimestamp: number;
  effectiveEndTimestamp: number;
  benefitCode: any;
  displayAmount: number;
  displayStartFee: string;
  benefitTitle: string;
  id: string;
  price: number;
  condition: string;
  description: string;
  isHighlighted?: boolean;
  effectiveEndTime?: number;
  status?: string; // 添加状态属性
  jumpUrl?: string; // 添加跳转链接属性
}

export default (props: Props) => {
  const { data } = props || {};
  const { $attr = {} } = data || {};
  const marginTop = $attr.marginTop !== undefined ? $attr.marginTop : 0;
  const marginBottom =
    $attr.marginBottom !== undefined ? $attr.marginBottom : 16;
  const title = $attr.title || "";
  const subTitle = $attr.subTitle || "";
  const jumpUrl = $attr.jumpUrl || "";
  const couponJumpUrl = $attr.couponJumpUrl || "";

  // 获取配置的模块背景色，如果没有配置则使用默认值
  const modBgColor = $attr.modBgColor || "#fff";

  // 获取配置的渠道和asac，如果没有配置则使用默认值
  const channel = $attr.channel || "lafite_PCqudao";
  const asac = $attr.asac || "2A24514KMM6TWY02RE4CPQ";
  const strategyCode = $attr.strategyCode || null;

  if (isTrue($attr?.hidden)) return null;

  const [coupons, setCoupons] = useState<CouponData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shouldShowModule, setShouldShowModule] = useState(true);
  const [claiming, setClaiming] = useState(false);
  const [hasReceived, setHasReceived] = useState(false);

  useEffect(() => {
    const fetchCoupons = async () => {
      try {
        setLoading(true);
        // 发起请求获取消费券数据
        const response = await requestRead({
          bizCode: "buy_back",
          bizParams: JSON.stringify({
            action: "jingxi_hb_show_with_amount",
            channel: channel,
            strategyCode: strategyCode,
            withOtherBenefitShow: false
          }),
          asac: asac,
        });
        if (isTrue(response.success)) {
          // 处理返回的数据
          const benefits = response?.data?.benefits || [];
          const winStatus = response?.data?.winStatus;

          // 根据winStatus设置hasReceived状态
          setHasReceived(winStatus === "hadWin");

          // 如果是hadWin状态，只显示有效的券
          let filteredBenefits = benefits;
          if (winStatus === "hadWin") {
            filteredBenefits = benefits.filter(benefit => benefit.status === "effective" || benefit.status === "inEffective");
          }

          if (filteredBenefits?.length > 0 && (winStatus === "canWin" || winStatus === "hadWin")) {
            setCoupons(filteredBenefits);
          } else {
            // 如果过滤后没有优惠券，隐藏模块
            setShouldShowModule(false);
          }
        } else {
          // 请求失败也隐藏模块
          setShouldShowModule(false);
          setError(response.message || "获取优惠券失败");
        }
      } catch (err) {
        // 请求异常也隐藏模块
        setShouldShowModule(false);
        setError("获取优惠券数据失败");
      } finally {
        setLoading(false);
      }
    };

    fetchCoupons();
  }, [channel, asac, strategyCode]);

  const hasValidCouponsToDisplay = shouldShowModule && coupons.length > 0;

  // 加载状态显示
  if (loading) {
    return (
      <AdaptLayout>
        <div
          style={{
            borderRadius: "12px",
            backgroundColor: modBgColor,
            paddingBottom: '16px',
          }}
        >
          {/* 不显示标题，保持与实际渲染结构一致 */}
          <AdaptRow style={{ margin: '0 8px', paddingTop: 12 }}>
            {[...Array(4)].map((_, index) => (
              <AdaptCol
                key={`skeleton-${index}`}
                span={LayoutRadioEnum.entire_row / 4}
              >
                <div className={styles.skeletonCoupon}>
                  <div className={styles.skeletonLeft}>
                    <div className={styles.skeletonPrice}></div>
                  </div>
                  <div className={styles.skeletonRight}>
                    <div className={styles.skeletonText}></div>
                    <div className={styles.skeletonText}></div>
                  </div>
                </div>
              </AdaptCol>
            ))}
          </AdaptRow>
        </div>
      </AdaptLayout>
    );
  }

  // 如果没有有效的优惠券，返回null
  if (!hasValidCouponsToDisplay) {
    return null;
  }

  // 一键领取所有优惠券
  const handleClaimAllCoupons = async () => {
    if (claiming || coupons.length === 0) return;
    try {
      setClaiming(true);
      // 调用写接口领取优惠券
      const response = await requestWrite({
        bizCode: "buy_back",
        bizParams: JSON.stringify({
          action: "jingxi_hb_issue",
          channel: channel,
          strategyCode: strategyCode,
          benefitCodes: coupons.map(coupon => coupon?.benefitCode).join(',') // 传递所有券的benefitCode
        }),
        asac: asac,
      });
      if (isTrue(response.success)) {
        toast("领取成功!", { type: "big", colorStyle: "white", zIndex: '100000002', icon: 'https://img.alicdn.com/imgextra/i3/O1CN01IG47eZ1gu6wpnIEnA_!!6000000004201-2-tps-144-144.png' });
        setHasReceived(true);
      } else {
        toast("领取失败，请稍后重试", { type: "big", colorStyle: "white", zIndex: '100000002', icon: 'https://img.alicdn.com/imgextra/i3/O1CN01nWBkpT1Wqlsl45vDV_!!6000000002840-2-tps-128-128.png' });
      }
    } catch (err) {
      console.error('领取优惠券失败:', err);
    } finally {
      setClaiming(false);
    }
  };

  return (
    <AdaptLayout style={{ marginBottom: `${marginBottom}px`, marginTop: `${marginTop}px` }}>
      <div
        style={{
          borderRadius: "12px",
          backgroundColor: modBgColor,
          paddingBottom: '16px',
        }}
      >
        {/* 标题区域 */}
        {title && (
          <Header>
            <MyHeader
              title={title}
              subTitle={subTitle}
              jumpUrl={jumpUrl}
              layout="one-row"
              isPackageCard={true}
              paddingTop="12"
              paddingBottom="0"
              style={{
                width: "calc(100% - 32px)",
                paddingLeft: "16px",
                paddingRight: "16px",
                backgroundColor: modBgColor,
              }}
            />
          </Header>
        )}
        <AdaptRow style={{ margin: '0 8px', justifyContent: coupons?.length === 1 ? 'center' : 'unset' }}>
          {coupons.map((coupon) => (
            <AdaptCol
              key={coupon.id}
              span={LayoutRadioEnum.entire_row / (coupons?.length >= 4 ? 4 : coupons?.length) }
              style={{ maxWidth: coupons?.length === 1 ? '720px' : 'unset' }}
            >
              <Coupon
                id={coupon.id}
                benefitTitle={coupon.benefitTitle}
                price={coupon?.displayAmount}
                displayStartFee={coupon.displayStartFee}
                description={coupon.benefitTitle}
                jumpUrl={couponJumpUrl}
                status={coupon.status}
                effectiveStartTimestamp={coupon?.effectiveStartTimestamp}
                effectiveEndTimestamp={coupon?.effectiveEndTimestamp}
                onClaim={coupons.length === 1 ? handleClaimAllCoupons : undefined}
                claiming={claiming}
                hasReceived={hasReceived}
                showClaimButton={coupons.length === 1}
                isWhiteTheme={modBgColor === '#fff'}
              />
            </AdaptCol>
          ))}
        </AdaptRow>

        {/* 底部一键领券按钮 - 只在多张券且canWin状态且未领取时显示 */}
        {!hasReceived && coupons.length > 1 && (
          <AdaptRow style={{ margin: '0 8px', justifyContent: 'center' }}>
            <div 
              className={styles.couponButton} 
              onClick={handleClaimAllCoupons}
              style={{ 
                opacity: claiming ? 0.6 : 1, 
                cursor: claiming ? 'not-allowed' : 'pointer' 
              }}
            >
              <div className={styles.buttonContent}>
                <span className={styles.buttonText}>
                  {claiming ? '领取中...' : '一键领取所有优惠券'}
                </span>
              </div>
            </div>
          </AdaptRow>
        )}
      </div>
    </AdaptLayout>
  );
};
