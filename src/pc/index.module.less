.container {
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;

  .couponRow {
    margin-top: 16px;
  }
}

// 内容区域样式已经在下面定义

// 内容区域样式
.content {
  padding: 0 16px 16px;
}

// 骨架屏样式
.skeletonBanner {
  width: 100%;
  height: 144px; // 调整为与实际 FloorBanner 高度一致 (144px)
  background: linear-gradient(90deg, #f5f5f5 25%, #eaeaea 50%, #f5f5f5 75%);
  background-size: 200% 100%;
  animation: shimmer 2s ease-in-out infinite;
  border-radius: 8px;
}

.skeletonCoupon {
  display: flex;
  height: 80px;
  background: #f8f8f8;
  border-radius: 12px;
  padding: 8px;
  animation: pulse 3s ease-in-out infinite;

  .skeletonLeft {
    width: 30%;
    max-width: 120px;
    display: flex;
    justify-content: center;
    align-items: center;

    .skeletonPrice {
      width: 70%;
      height: 28px;
      background: linear-gradient(90deg, #f5f5f5 25%, #eaeaea 50%, #f5f5f5 75%);
      background-size: 200% 100%;
      animation: shimmer 2s ease-in-out infinite;
      border-radius: 4px;
    }
  }

  .skeletonRight {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 12px;

    .skeletonText {
      height: 16px;
      background: linear-gradient(90deg, #f5f5f5 25%, #eaeaea 50%, #f5f5f5 75%);
      background-size: 200% 100%;
      animation: shimmer 2s ease-in-out infinite;
      border-radius: 4px;
      margin-bottom: 8px;

      &:first-child {
        width: 70%;
      }

      &:last-child {
        width: 50%;
      }
    }
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 0.95;
  }
  100% {
    opacity: 0.8;
  }
}


.couponButton {
  border-radius: 8px;
  background: #FF0036;
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  box-sizing: border-box;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  width: 158px;
  height: 40px;
  cursor: pointer;
  margin-top: 12px;
}

.buttonContent {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  box-sizing: border-box;
  padding: 1px 4px;
  align-items: center;
  justify-content: center;
  width: 134px;
  height: 24px;
}

.buttonText {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: #FFFFFF;
}
