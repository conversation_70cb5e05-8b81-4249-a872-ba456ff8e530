{"name": "@ali/alimod-tbpc-consum-coupon-ice", "version": "1.0.4", "description": "alimod-tbpc-consum-coupon-ice", "scripts": {"start": "ice-pkg start", "build": "ice-pkg build", "prepublishOnly": "npm run build", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "lint": "npm run eslint && npm run stylelint", "preview": "ark dev"}, "dependencies": {"@ali/alimod-tbpc-floor-banner-ice": "^1.0.2", "@ali/pcom-ark-mod-utils": "^2.0.12", "@ali/pcom-ark-pc-toast": "^1.0.5", "@ali/pcom-ice-text": "^1.0.2", "@ali/pcom-ice-view": "^1.0.2", "@ali/pcom-tbpc-component-venue-modheader": "^1.0.2", "@ali/pcom-tbpc-mod-container": "^1.1.23", "@ali/pcom-tbpc-venue-utils": "^1.0.0", "@ali/pcom-uniapi-solution-mtop": "^1.0.0", "@ali/picture": "^2.5.1", "@ice/jsx-runtime": "^0.2.0", "@swc/helpers": "^0.5.1"}, "devDependencies": {"@ali/ark-ice-builder": "^2.1.2", "@ali/build-plugin-ice-pegasus-pkg": "^1.0.56", "@ali/build-plugin-pegasus-base-ssr": "^2.1.0", "@ali/pkg-plugin-dev": "^1.0.0", "@ali/pkg-plugin-dev-client": "^1.0.0", "@applint/spec": "^1.2.3", "@ice/pkg": "^1.0.0", "@ice/pkg-plugin-jsx-plus": "^1.0.0", "@ice/runtime": "^1.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.3.1", "body-parser": "^1.20.2", "eslint": "^8.0.0", "less": "^4.1.3", "less-loader": "^11.1.0"}, "files": ["esm", "es2017", "cjs", "dist"], "main": "esm/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "exports": {".": {"es2017": {"types": "./es2017/index.d.ts", "default": "./es2017/index.js"}, "default": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": "./*"}, "sideEffects": ["dist/*", "*.scss", "*.less", "*.css"], "keywords": ["ice", "react", "component"], "peerDependencies": {"react": "^17 || ^18"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT"}